const countdownEl = document.getElementById("countdown");
const countdownContainer = document.getElementById("countdown-container");
const contentEl = document.getElementById("content");

// 🎯 Set your target birthday date and time here (12:00 AM)
const birthday = new Date("2025-06-23T04:32:00");

// Audio element for birthday music
const audio = new Audio('audio.mp3');
audio.preload = 'auto';
audio.volume = 0.7; // Set volume to 70%

// Background carousel variables
let currentBgSlide = 0;
const bgSlides = document.querySelectorAll('.bg-slide');

// Photo background carousel variables for content
let currentPhotoSlide = 0;
const photoSlides = document.querySelectorAll('.photo-bg-slide');
const totalPhotoSlides = photoSlides.length;

// Page-wise scrolling variables
let currentPage = 0;
let isScrolling = false;
const totalPages = 6; // Number of sections

function updateCountdown() {
  const now = new Date();
  const diff = birthday - now;

  if (diff <= 0) {
    // Countdown finished - play audio and show content
    countdownContainer.style.display = "none";
    contentEl.classList.remove("hidden");
    clearInterval(timer);

    // Hide BG carousel and show only photo backgrounds
    hideBgCarousel();

    // Play birthday audio automatically
    playBirthdayAudio();

    // Start photo background carousel and page-wise scrolling
    startPhotoCarousel();
    initializePageScrolling();
    return;
  }

  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  const hours = Math.floor((diff / (1000 * 60 * 60)) % 24);
  const minutes = Math.floor((diff / (1000 * 60)) % 60);
  const seconds = Math.floor((diff / 1000) % 60);

  // Check if we're in the final minute
  const totalSecondsLeft = Math.floor(diff / 1000);
  if (totalSecondsLeft <= 60 && totalSecondsLeft > 0) {
    countdownEl.classList.add('final-minute');
  } else {
    countdownEl.classList.remove('final-minute');
  }

  // Clean up countdown display - remove insignificant parts
  let countdownText = '';
  if (days > 0) countdownText += `${days}d `;
  if (hours > 0 || days > 0) countdownText += `${hours}h `;
  if (minutes > 0 || hours > 0 || days > 0) countdownText += `${minutes}m `;
  countdownText += `${seconds}s`;

  countdownEl.innerHTML = countdownText.trim();
}

function playBirthdayAudio() {
  // Try to play audio automatically
  const playPromise = audio.play();

  if (playPromise !== undefined) {
    playPromise.catch(() => {
      // If autoplay fails, try again after a short delay
      setTimeout(() => {
        audio.play().catch(() => {
          console.log('Audio autoplay prevented by browser policy.');
        });
      }, 1000);
    });
  }
}

// Background carousel functionality
function rotateBackground() {
  bgSlides[currentBgSlide].classList.remove('active');
  currentBgSlide = (currentBgSlide + 1) % bgSlides.length;
  bgSlides[currentBgSlide].classList.add('active');
}

// Hide BG carousel after countdown
function hideBgCarousel() {
  const bgCarousel = document.querySelector('.bg-carousel');
  if (bgCarousel) {
    bgCarousel.style.opacity = '0';
    setTimeout(() => {
      bgCarousel.style.display = 'none';
    }, 1000);
  }
}

// Photo background carousel functionality for content
function rotatePhotoBackground() {
  if (photoSlides.length === 0) return;

  photoSlides[currentPhotoSlide].classList.remove('active');
  currentPhotoSlide = (currentPhotoSlide + 1) % totalPhotoSlides;
  photoSlides[currentPhotoSlide].classList.add('active');
}

function startPhotoCarousel() {
  // Show first photo background
  if (photoSlides.length > 0) {
    photoSlides[0].classList.add('active');
  }
}

// Initialize page-wise scrolling
function initializePageScrolling() {
  const scrollContent = document.querySelector('.scroll-content');
  if (!scrollContent) return;

  // Enable scrolling for content
  enableScrolling();

  // Disable default scrolling for scroll content
  scrollContent.style.overflow = 'hidden';

  // Show page indicators
  const pageIndicators = document.querySelector('.page-indicators');
  if (pageIndicators) {
    pageIndicators.classList.add('show');
  }

  // Add wheel event listener for page-wise scrolling
  scrollContent.addEventListener('wheel', handleWheelScroll, { passive: false });

  // Add touch events for mobile
  let touchStartY = 0;
  scrollContent.addEventListener('touchstart', (e) => {
    touchStartY = e.touches[0].clientY;
  }, { passive: true });

  scrollContent.addEventListener('touchend', (e) => {
    const touchEndY = e.changedTouches[0].clientY;
    const diff = touchStartY - touchEndY;

    if (Math.abs(diff) > 50) { // Minimum swipe distance
      if (diff > 0) {
        scrollToNextPage();
      } else {
        scrollToPrevPage();
      }
    }
  }, { passive: true });

  // Show first page
  showPage(0);
}

function handleWheelScroll(e) {
  e.preventDefault();

  if (isScrolling) return;

  if (e.deltaY > 0) {
    scrollToNextPage();
  } else {
    scrollToPrevPage();
  }
}

function scrollToNextPage() {
  if (currentPage < totalPages - 1) {
    currentPage++;
    showPage(currentPage);
  }
}

function scrollToPrevPage() {
  if (currentPage > 0) {
    currentPage--;
    showPage(currentPage);
  }
}

function showPage(pageIndex) {
  if (isScrolling) return;

  isScrolling = true;

  // Change photo background
  if (photoSlides.length > 0) {
    photoSlides[currentPhotoSlide].classList.remove('active');
    currentPhotoSlide = pageIndex % totalPhotoSlides;
    photoSlides[currentPhotoSlide].classList.add('active');
  }

  // Update page indicators
  updatePageIndicators(pageIndex);

  // Get all sections
  const sections = document.querySelectorAll('.memory-section, .message-section');

  // Hide all sections first
  sections.forEach(section => {
    section.classList.remove('active');
    section.style.opacity = '0';
    section.style.transform = 'translateY(50px) scale(0.9)';
    section.style.display = 'none';
  });

  // Show current section with morph effect
  if (sections[pageIndex]) {
    const currentSection = sections[pageIndex];
    currentSection.style.display = 'flex';

    setTimeout(() => {
      currentSection.style.opacity = '1';
      currentSection.style.transform = 'translateY(0) scale(1)';
      currentSection.classList.add('active');
    }, 100);
  }

  setTimeout(() => {
    isScrolling = false;
  }, 800);
}

function updatePageIndicators(activeIndex) {
  const dots = document.querySelectorAll('.page-dot');
  dots.forEach((dot, index) => {
    dot.classList.toggle('active', index === activeIndex);
  });
}

function goToPage(pageIndex) {
  if (pageIndex >= 0 && pageIndex < totalPages) {
    currentPage = pageIndex;
    showPage(currentPage);
  }
}

// Block scrolling during countdown
function blockScrolling() {
  document.body.style.overflow = 'hidden';
  document.documentElement.style.overflow = 'hidden';
}

function enableScrolling() {
  document.body.style.overflow = 'auto';
  document.documentElement.style.overflow = 'auto';
}

// Initialize everything
const timer = setInterval(updateCountdown, 1000);
updateCountdown();

// Start background carousel
setInterval(rotateBackground, 5000);

// Block scrolling during countdown
blockScrolling();

// Initialize everything when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  // Block scrolling initially
  blockScrolling();
});
