const countdownEl = document.getElementById("countdown");
const countdownContainer = document.getElementById("countdown-container");
const contentEl = document.getElementById("content");

// 🎯 Set your target birthday date and time here (12:00 AM)
const birthday = new Date("2025-06-23T03:44:00");

// Audio element for birthday music
const audio = new Audio('audio.mp3');
audio.preload = 'auto';
audio.volume = 0.5; // Set volume to 70%

// Background carousel variables
let currentBgSlide = 0;
const bgSlides = document.querySelectorAll('.bg-slide');

// Photo background carousel variables for content
let currentPhotoSlide = 0;
const photoSlides = document.querySelectorAll('.photo-bg-slide');
const totalPhotoSlides = photoSlides.length;

// Page-wise scrolling variables
let currentPage = 0;
let isScrolling = false;
let scrollTimeout = null;
const totalPages = 6; // Number of sections
const scrollDelay = 800; // Delay between scrolls in milliseconds

// Floating elements variables
let floatingInterval = null;
const balloonEmojis = ['🎈', '🎀', '🎁', '🎂', '🎉', '🎊'];
const heartEmojis = ['💖', '💕', '💗', '💝', '💘', '💞'];
const balloonColors = ['red', 'blue', 'yellow', 'pink', 'green', 'purple'];
const heartColors = ['pink', 'red', 'purple', 'gold'];

function updateCountdown() {
  const now = new Date();
  const diff = birthday - now;

  if (diff <= 0) {
    // Countdown finished - play audio and show content
    countdownContainer.style.display = "none";
    contentEl.classList.remove("hidden");
    clearInterval(timer);

    // Hide BG carousel and show only photo backgrounds
    hideBgCarousel();

    // Play birthday audio automatically
    playBirthdayAudio();

    // Start photo background carousel and page-wise scrolling
    startPhotoCarousel();
    initializePageScrolling();

    // Start floating balloons and hearts
    startFloatingElements();
    return;
  }

  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  const hours = Math.floor((diff / (1000 * 60 * 60)) % 24);
  const minutes = Math.floor((diff / (1000 * 60)) % 60);
  const seconds = Math.floor((diff / 1000) % 60);

  // Check if we're in the final minute
  const totalSecondsLeft = Math.floor(diff / 1000);
  if (totalSecondsLeft <= 60 && totalSecondsLeft > 0) {
    countdownEl.classList.add('final-minute');

    // Start some floating elements in final minute for excitement
    if (!floatingInterval) {
      floatingInterval = setInterval(() => {
        if (Math.random() < 0.4) createFloatingElement('heart');
        if (Math.random() < 0.3) createFloatingElement('balloon');
      }, 2000);
    }
  } else {
    countdownEl.classList.remove('final-minute');
  }

  // Clean up countdown display - remove insignificant parts
  let countdownText = '';
  if (days > 0) countdownText += `${days}d `;
  if (hours > 0 || days > 0) countdownText += `${hours}h `;
  if (minutes > 0 || hours > 0 || days > 0) countdownText += `${minutes}m `;
  countdownText += `${seconds}s`;

  countdownEl.innerHTML = countdownText.trim();
}

function playBirthdayAudio() {
  // Multiple attempts to play audio
  attemptAudioPlay();
}

function attemptAudioPlay() {
  // Try immediate play
  audio.play().then(() => {
    console.log('Audio playing successfully');
  }).catch(() => {
    // If immediate play fails, try with user interaction
    createAudioTrigger();

    // Also try playing on any user interaction
    setupGlobalAudioTriggers();
  });
}

function createAudioTrigger() {
  // Create a visible play button for mobile
  const audioButton = document.createElement('button');
  audioButton.innerHTML = '🎵 Tap to Play Music';
  audioButton.style.cssText = `
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    padding: 15px 25px;
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
    color: white;
    border: none;
    border-radius: 25px;
    font-size: 1.2rem;
    cursor: pointer;
    z-index: 10000;
    box-shadow: 0 4px 15px rgba(0,0,0,0.3);
    animation: pulse 2s infinite;
  `;

  const playAudio = () => {
    audio.play().then(() => {
      audioButton.remove();
      console.log('Audio started via button');
    }).catch((error) => {
      console.log('Audio play failed:', error);
      audioButton.innerHTML = '🔇 Audio Unavailable';
      setTimeout(() => audioButton.remove(), 3000);
    });
  };

  audioButton.addEventListener('click', playAudio);
  audioButton.addEventListener('touchend', playAudio);

  document.body.appendChild(audioButton);

  // Auto-remove after 15 seconds if not clicked
  setTimeout(() => {
    if (audioButton.parentNode) {
      audioButton.remove();
    }
  }, 15000);
}

function setupGlobalAudioTriggers() {
  let audioStarted = false;

  const tryPlayAudio = () => {
    if (!audioStarted) {
      audio.play().then(() => {
        audioStarted = true;
        console.log('Audio started via global trigger');
        // Remove all event listeners once audio starts
        document.removeEventListener('touchstart', tryPlayAudio);
        document.removeEventListener('click', tryPlayAudio);
        document.removeEventListener('scroll', tryPlayAudio);
      }).catch(() => {
        // Silent fail for global triggers
      });
    }
  };

  // Try on any user interaction
  document.addEventListener('touchstart', tryPlayAudio, { once: true, passive: true });
  document.addEventListener('click', tryPlayAudio, { once: true });
  document.addEventListener('scroll', tryPlayAudio, { once: true, passive: true });
}

// Background carousel functionality
function rotateBackground() {
  bgSlides[currentBgSlide].classList.remove('active');
  currentBgSlide = (currentBgSlide + 1) % bgSlides.length;
  bgSlides[currentBgSlide].classList.add('active');
}

// Hide BG carousel after countdown
function hideBgCarousel() {
  const bgCarousel = document.querySelector('.bg-carousel');
  if (bgCarousel) {
    bgCarousel.style.opacity = '0';
    setTimeout(() => {
      bgCarousel.style.display = 'none';
    }, 1000);
  }
}

// Photo background carousel functionality for content
function rotatePhotoBackground() {
  if (photoSlides.length === 0) return;

  photoSlides[currentPhotoSlide].classList.remove('active');
  currentPhotoSlide = (currentPhotoSlide + 1) % totalPhotoSlides;
  photoSlides[currentPhotoSlide].classList.add('active');
}

function startPhotoCarousel() {
  // Show first photo background
  if (photoSlides.length > 0) {
    photoSlides[0].classList.add('active');
  }
}

// Initialize page-wise scrolling
function initializePageScrolling() {
  const scrollContent = document.querySelector('.scroll-content');
  if (!scrollContent) return;

  // Enable scrolling for content
  enableScrolling();

  // Disable default scrolling for scroll content
  scrollContent.style.overflow = 'hidden';

  // Show page indicators
  const pageIndicators = document.querySelector('.page-indicators');
  if (pageIndicators) {
    pageIndicators.classList.add('show');
  }

  // Add wheel event listener for page-wise scrolling
  scrollContent.addEventListener('wheel', handleWheelScroll, { passive: false });

  // Add touch events for mobile
  let touchStartY = 0;
  scrollContent.addEventListener('touchstart', (e) => {
    touchStartY = e.touches[0].clientY;
  }, { passive: true });

  scrollContent.addEventListener('touchend', (e) => {
    const touchEndY = e.changedTouches[0].clientY;
    const diff = touchStartY - touchEndY;

    if (Math.abs(diff) > 50) { // Minimum swipe distance
      if (diff > 0) {
        scrollToNextPage();
      } else {
        scrollToPrevPage();
      }
    }
  }, { passive: true });

  // Show first page
  showPage(0);
}

function handleWheelScroll(e) {
  e.preventDefault();

  if (isScrolling) return;

  // Clear existing timeout
  if (scrollTimeout) {
    clearTimeout(scrollTimeout);
  }

  // Set new timeout to prevent rapid scrolling
  scrollTimeout = setTimeout(() => {
    if (e.deltaY > 0) {
      scrollToNextPage();
    } else {
      scrollToPrevPage();
    }
  }, 50); // Small delay to debounce scroll events
}

function scrollToNextPage() {
  if (currentPage < totalPages - 1) {
    currentPage++;
    showPage(currentPage);
  }
}

function scrollToPrevPage() {
  if (currentPage > 0) {
    currentPage--;
    showPage(currentPage);
  }
}

function showPage(pageIndex) {
  if (isScrolling) return;

  isScrolling = true;

  // Change photo background
  if (photoSlides.length > 0) {
    photoSlides[currentPhotoSlide].classList.remove('active');
    currentPhotoSlide = pageIndex % totalPhotoSlides;
    photoSlides[currentPhotoSlide].classList.add('active');
  }

  // Update page indicators
  updatePageIndicators(pageIndex);

  // Get all sections
  const sections = document.querySelectorAll('.memory-section, .message-section');

  // Hide all sections first
  sections.forEach(section => {
    section.classList.remove('active');
    section.style.opacity = '0';
    section.style.transform = 'translateY(50px) scale(0.9)';
    section.style.display = 'none';
  });

  // Show current section with morph effect
  if (sections[pageIndex]) {
    const currentSection = sections[pageIndex];
    currentSection.style.display = 'flex';

    setTimeout(() => {
      currentSection.style.opacity = '1';
      currentSection.style.transform = 'translateY(0) scale(1)';
      currentSection.classList.add('active');

      // Create a burst of floating elements when page loads
      createFloatingBurst();
    }, 100);
  }

  setTimeout(() => {
    isScrolling = false;
  }, scrollDelay);
}

function updatePageIndicators(activeIndex) {
  const dots = document.querySelectorAll('.page-dot');
  dots.forEach((dot, index) => {
    dot.classList.toggle('active', index === activeIndex);
  });
}

function goToPage(pageIndex) {
  if (pageIndex >= 0 && pageIndex < totalPages) {
    currentPage = pageIndex;
    showPage(currentPage);
  }
}

// Floating Elements Functions
function createFloatingElement(type) {
  const container = document.getElementById('floating-elements');
  if (!container) return;

  const element = document.createElement('div');
  element.className = type;

  // Random properties
  const isLarge = Math.random() < 0.3; // 30% chance of large size
  const hasSway = Math.random() < 0.5; // 50% chance of sway animation

  if (isLarge) element.classList.add('large');
  if (hasSway) element.classList.add('sway');

  // Set content and color
  if (type === 'balloon') {
    element.textContent = balloonEmojis[Math.floor(Math.random() * balloonEmojis.length)];
    element.classList.add(balloonColors[Math.floor(Math.random() * balloonColors.length)]);
  } else if (type === 'heart') {
    element.textContent = heartEmojis[Math.floor(Math.random() * heartEmojis.length)];
    element.classList.add(heartColors[Math.floor(Math.random() * heartColors.length)]);
  }

  // Random horizontal position
  element.style.left = Math.random() * 90 + '%';

  // Random animation delay
  element.style.animationDelay = Math.random() * 2 + 's';

  container.appendChild(element);

  // Remove element after animation completes
  const animationDuration = isLarge ? (type === 'balloon' ? 10000 : 8000) : (type === 'balloon' ? 8000 : 6000);
  setTimeout(() => {
    if (element.parentNode) {
      element.remove();
    }
  }, animationDuration + 2000);
}

function startFloatingElements() {
  if (floatingInterval) return; // Already running

  floatingInterval = setInterval(() => {
    // Create balloons and hearts randomly
    if (Math.random() < 0.7) { // 70% chance
      createFloatingElement('balloon');
    }
    if (Math.random() < 0.6) { // 60% chance
      createFloatingElement('heart');
    }
  }, 1500); // Create new elements every 1.5 seconds
}

function stopFloatingElements() {
  if (floatingInterval) {
    clearInterval(floatingInterval);
    floatingInterval = null;
  }

  // Clear existing elements
  const container = document.getElementById('floating-elements');
  if (container) {
    container.innerHTML = '';
  }
}

// Block scrolling during countdown
function blockScrolling() {
  document.body.style.overflow = 'hidden';
  document.documentElement.style.overflow = 'hidden';
}

function enableScrolling() {
  document.body.style.overflow = 'auto';
  document.documentElement.style.overflow = 'auto';
}

// Initialize everything
const timer = setInterval(updateCountdown, 1000);
updateCountdown();

// Start background carousel
setInterval(rotateBackground, 5000);

// Block scrolling during countdown
blockScrolling();

// Initialize everything when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  // Block scrolling initially
  blockScrolling();
});
