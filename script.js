const countdownEl = document.getElementById("countdown");
const countdownContainer = document.getElementById("countdown-container");
const contentEl = document.getElementById("content");

// 🎯 Set your target birthday date and time here (12:00 AM)
const birthday = new Date("2025-06-21T00:00:00");

function updateCountdown() {
  const now = new Date();
  const diff = birthday - now;

  if (diff <= 0) {
    // Countdown finished
    countdownContainer.style.display = "none";
    contentEl.classList.remove("hidden");
    clearInterval(timer);
    return;
  }

  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  const hours = Math.floor((diff / (1000 * 60 * 60)) % 24);
  const minutes = Math.floor((diff / (1000 * 60)) % 60);
  const seconds = Math.floor((diff / 1000) % 60);

  countdownEl.innerHTML = `${days}d ${hours}h ${minutes}m ${seconds}s`;
}

const timer = setInterval(updateCountdown, 1000);
updateCountdown();
