const countdownEl = document.getElementById("countdown");
const countdownContainer = document.getElementById("countdown-container");
const contentEl = document.getElementById("content");

// 🎯 Set your target birthday date and time here (12:00 AM)
const birthday = new Date("2025-06-25T00:00:00");

// Audio element for birthday music
const audio = new Audio('audio.mp3');
audio.preload = 'auto';

// Background carousel variables
let currentBgSlide = 0;
const bgSlides = document.querySelectorAll('.bg-slide');

// Image carousel variables
let currentSlide = 0;
const slides = document.querySelectorAll('.carousel-slide');
const totalSlides = slides.length;

function updateCountdown() {
  const now = new Date();
  const diff = birthday - now;

  if (diff <= 0) {
    // Countdown finished - play audio and show content
    countdownContainer.style.display = "none";
    contentEl.classList.remove("hidden");
    clearInterval(timer);

    // Play birthday audio
    playBirthdayAudio();
    return;
  }

  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  const hours = Math.floor((diff / (1000 * 60 * 60)) % 24);
  const minutes = Math.floor((diff / (1000 * 60)) % 60);
  const seconds = Math.floor((diff / 1000) % 60);

  // Check if we're in the final minute
  const totalSecondsLeft = Math.floor(diff / 1000);
  if (totalSecondsLeft <= 60 && totalSecondsLeft > 0) {
    countdownEl.classList.add('final-minute');
  } else {
    countdownEl.classList.remove('final-minute');
  }

  countdownEl.innerHTML = `${days}d ${hours}h ${minutes}m ${seconds}s`;
}

function playBirthdayAudio() {
  // Handle autoplay restrictions by trying to play and catching errors
  audio.play().catch(() => {
    console.log('Audio autoplay prevented. User interaction required.');
    // Create a play button if autoplay fails
    const playButton = document.createElement('button');
    playButton.innerHTML = '🎵 Play Birthday Song';
    playButton.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 15px 25px;
      background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
      color: white;
      border: none;
      border-radius: 25px;
      font-size: 1rem;
      cursor: pointer;
      z-index: 1000;
      box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    `;
    playButton.onclick = () => {
      audio.play();
      playButton.remove();
    };
    document.body.appendChild(playButton);
  });
}

// Background carousel functionality
function rotateBackground() {
  bgSlides[currentBgSlide].classList.remove('active');
  currentBgSlide = (currentBgSlide + 1) % bgSlides.length;
  bgSlides[currentBgSlide].classList.add('active');
}

// Image carousel functionality
function moveCarousel(direction) {
  currentSlide += direction;

  if (currentSlide >= totalSlides) {
    currentSlide = 0;
  } else if (currentSlide < 0) {
    currentSlide = totalSlides - 1;
  }

  updateCarousel();
}

function updateCarousel() {
  const track = document.querySelector('.carousel-track');
  track.style.transform = `translateX(-${currentSlide * 100}%)`;

  // Update dots
  updateDots();
}

function createDots() {
  const dotsContainer = document.querySelector('.carousel-dots');
  for (let i = 0; i < totalSlides; i++) {
    const dot = document.createElement('div');
    dot.classList.add('dot');
    if (i === 0) dot.classList.add('active');
    dot.addEventListener('click', () => goToSlide(i));
    dotsContainer.appendChild(dot);
  }
}

function updateDots() {
  const dots = document.querySelectorAll('.dot');
  dots.forEach((dot, index) => {
    dot.classList.toggle('active', index === currentSlide);
  });
}

function goToSlide(slideIndex) {
  currentSlide = slideIndex;
  updateCarousel();
}

// Auto-advance carousel
function autoAdvanceCarousel() {
  currentSlide = (currentSlide + 1) % totalSlides;
  updateCarousel();
}

// Initialize everything
const timer = setInterval(updateCountdown, 1000);
updateCountdown();

// Start background carousel
setInterval(rotateBackground, 5000);

// Touch support for mobile devices
let touchStartX = 0;
let touchEndX = 0;

function handleTouchStart(e) {
  touchStartX = e.changedTouches[0].screenX;
}

function handleTouchEnd(e) {
  touchEndX = e.changedTouches[0].screenX;
  handleSwipe();
}

function handleSwipe() {
  const swipeThreshold = 50;
  const diff = touchStartX - touchEndX;

  if (Math.abs(diff) > swipeThreshold) {
    if (diff > 0) {
      // Swipe left - next slide
      moveCarousel(1);
    } else {
      // Swipe right - previous slide
      moveCarousel(-1);
    }
  }
}

// Add touch event listeners to carousel
document.addEventListener('DOMContentLoaded', () => {
  const carousel = document.querySelector('.carousel');
  if (carousel) {
    carousel.addEventListener('touchstart', handleTouchStart, { passive: true });
    carousel.addEventListener('touchend', handleTouchEnd, { passive: true });
  }
});

// Initialize image carousel when content is shown
setTimeout(() => {
  if (!contentEl.classList.contains('hidden')) {
    createDots();
    setInterval(autoAdvanceCarousel, 4000);
  }
}, 100);
