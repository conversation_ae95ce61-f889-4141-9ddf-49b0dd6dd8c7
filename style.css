body {
  margin: 0;
  padding: 0;
  font-family: 'Playfair Display', serif;
  background: linear-gradient(to right, #ffe4e1, #fff0f5);
  overflow-x: hidden;
  color: #333;
}

.container {
  text-align: center;
  padding: 3rem 1rem;
  position: relative;
  z-index: 1;
}

h1 {
  font-size: 3rem;
  font-family: 'Dancing Script', cursive;
  animation: fadeInDown 2s ease;
}

#countdown {
  font-size: 2.5rem;
  margin: 2rem 0;
  animation: pulse 2s infinite;
}

.hidden {
  display: none;
}

.wish {
  font-size: 2.5rem;
  color: #d6336c;
  animation: fadeInUp 2s ease forwards;
}

.message {
  font-size: 1.3rem;
  margin-top: 1rem;
  animation: fadeInUp 2s ease 0.5s forwards;
}

.gallery {
  margin-top: 2rem;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 1rem;
  animation: fadeIn 2s ease 1s forwards;
}

.gallery img {
  width: 200px;
  height: 200px;
  object-fit: cover;
  border-radius: 15px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  transition: transform 0.3s ease;
}

.gallery img:hover {
  transform: scale(1.05);
}

/* Background Elements */
.background-elements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 0;
}

.hearts, .stars {
  position: absolute;
  width: 100%;
  height: 100%;
  background-image: url('https://www.transparenttextures.com/patterns/hearts.png');
  background-size: 200px;
  opacity: 0.06;
  animation: slowScroll 60s linear infinite;
}

.stars {
  background-image: url('https://www.transparenttextures.com/patterns/stardust.png');
  animation-duration: 80s;
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(40px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-40px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes slowScroll {
  0% { background-position: 0 0; }
  100% { background-position: 0 1000px; }
}
