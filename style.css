body {
  margin: 0;
  padding: 0;
  font-family: 'Playfair Display', serif;
  background: linear-gradient(45deg, #ff6b6b, #feca57, #48dbfb, #ff9ff3);
  background-size: 400% 400%;
  animation: gradientShift 15s ease infinite;
  overflow-x: hidden;
  color: #333;
  min-height: 100vh;
}

.container {
  text-align: center;
  padding: 2rem 1rem;
  position: relative;
  z-index: 1;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

#countdown-container {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
}

#countdown-container h1 {
  font-size: clamp(2rem, 8vw, 4rem);
  font-family: 'Dancing Script', cursive;
  animation: fadeInDown 2s ease;
  margin-bottom: 2rem;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

#countdown {
  font-size: clamp(3rem, 15vw, 8rem);
  margin: 2rem 0;
  animation: pulse 2s infinite;
  font-weight: bold;
  text-shadow: 3px 3px 6px rgba(0,0,0,0.4);
  background: linear-gradient(45deg, #ff9a9e, #fecfef, #fecfef);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: 0.1em;
}

#countdown.final-minute {
  font-size: clamp(4rem, 20vw, 12rem);
  animation: finalCountdown 1s infinite;
  color: #ff4757;
  text-shadow: 0 0 20px #ff4757, 0 0 40px #ff4757;
}

h1 {
  font-size: 3rem;
  font-family: 'Dancing Script', cursive;
  animation: fadeInDown 2s ease;
}

.hidden {
  display: none;
}

.wish {
  font-size: 2.5rem;
  color: #d6336c;
  animation: fadeInUp 2s ease forwards;
}

.message {
  font-size: 1.3rem;
  margin-top: 1rem;
  animation: fadeInUp 2s ease 0.5s forwards;
}

/* Image Carousel Styles */
.carousel-container {
  margin-top: 2rem;
  position: relative;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
  animation: fadeIn 2s ease 1s forwards;
}

.carousel {
  overflow: hidden;
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.carousel-track {
  display: flex;
  transition: transform 0.5s ease;
}

.carousel-slide {
  min-width: 100%;
  height: 400px;
  object-fit: cover;
  display: block;
}

.carousel-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.9);
  border: none;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  font-size: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 10;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.carousel-btn:hover {
  background: white;
  transform: translateY(-50%) scale(1.1);
}

.carousel-btn.prev {
  left: 10px;
}

.carousel-btn.next {
  right: 10px;
}

.carousel-dots {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-top: 20px;
}

.dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: all 0.3s ease;
}

.dot.active {
  background: white;
  transform: scale(1.2);
}

/* Background Elements */
.background-elements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 0;
}

.hearts, .stars {
  position: absolute;
  width: 100%;
  height: 100%;
  background-image: url('https://www.transparenttextures.com/patterns/hearts.png');
  background-size: 200px;
  opacity: 0.06;
  animation: slowScroll 60s linear infinite;
}

.stars {
  background-image: url('https://www.transparenttextures.com/patterns/stardust.png');
  animation-duration: 80s;
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(40px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-40px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes slowScroll {
  0% { background-position: 0 0; }
  100% { background-position: 0 1000px; }
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes finalCountdown {
  0%, 100% {
    transform: scale(1) rotate(0deg);
    text-shadow: 0 0 20px #ff4757, 0 0 40px #ff4757;
  }
  50% {
    transform: scale(1.1) rotate(2deg);
    text-shadow: 0 0 30px #ff4757, 0 0 60px #ff4757, 0 0 80px #ff4757;
  }
}

/* Background Image Carousel */
.bg-carousel {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  overflow: hidden;
}

.bg-slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  opacity: 0;
  transition: opacity 2s ease-in-out;
}

.bg-slide.active {
  opacity: 0.3;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .container {
    padding: 1rem 0.5rem;
  }

  #countdown-container h1 {
    font-size: clamp(1.5rem, 6vw, 3rem);
    margin-bottom: 1rem;
  }

  #countdown {
    font-size: clamp(2rem, 12vw, 6rem);
    margin: 1rem 0;
  }

  #countdown.final-minute {
    font-size: clamp(3rem, 16vw, 8rem);
  }

  .carousel-container {
    margin-top: 1rem;
  }

  .carousel-slide {
    height: 250px;
  }

  .carousel-btn {
    width: 40px;
    height: 40px;
    font-size: 1.2rem;
  }

  .carousel-btn.prev {
    left: 5px;
  }

  .carousel-btn.next {
    right: 5px;
  }
}

@media (max-width: 480px) {
  #countdown-container h1 {
    font-size: clamp(1.2rem, 5vw, 2.5rem);
  }

  #countdown {
    font-size: clamp(1.8rem, 10vw, 4rem);
  }

  #countdown.final-minute {
    font-size: clamp(2.5rem, 14vw, 6rem);
  }

  .carousel-slide {
    height: 200px;
  }

  .carousel-btn {
    width: 35px;
    height: 35px;
    font-size: 1rem;
  }

  .dot {
    width: 10px;
    height: 10px;
  }
}
