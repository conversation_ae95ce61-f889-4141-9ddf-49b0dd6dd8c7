body {
  margin: 0;
  padding: 0;
  font-family: 'Playfair Display', serif;
  background: linear-gradient(45deg, #ff6b6b, #feca57, #48dbfb, #ff9ff3);
  background-size: 400% 400%;
  animation: gradientShift 15s ease infinite;
  overflow-x: hidden;
  color: #333;
  min-height: 100vh;
  transition: all 0.3s ease;
}

/* Morph transition for responsive changes */
* {
  transition: font-size 0.3s ease, padding 0.3s ease, margin 0.3s ease, width 0.3s ease, height 0.3s ease;
}

.container {
  text-align: center;
  padding: 2rem 1rem;
  position: relative;
  z-index: 1;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

#countdown-container {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
}

#countdown-container h1 {
  font-size: clamp(2.5rem, 8vw, 5rem);
  font-family: 'Great Vibes', cursive;
  animation: fadeInDown 2s ease;
  margin-bottom: 2rem;
  color: #ffffff;
  text-shadow: 3px 3px 8px rgba(0,0,0,0.8), 0 0 20px rgba(255,255,255,0.5);
  font-weight: 400;
  background: rgba(0,0,0,0.3);
  padding: 1rem 2rem;
  border-radius: 20px;
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255,255,255,0.2);
}

#countdown {
  font-size: clamp(3.5rem, 15vw, 9rem);
  margin: 2rem 0;
  font-weight: bold;
  color: #ffffff;
  text-shadow: 4px 4px 12px rgba(0,0,0,0.9), 0 0 30px rgba(255,255,255,0.6);
  letter-spacing: 0.1em;
  font-family: 'Poppins', sans-serif;
  background: rgba(0,0,0,0.4);
  padding: 1.5rem 3rem;
  border-radius: 25px;
  backdrop-filter: blur(15px);
  border: 3px solid rgba(255,255,255,0.3);
}

#countdown.final-minute {
  font-size: clamp(4.5rem, 20vw, 13rem);
  animation: finalCountdown 1s infinite;
  color: #ff4757;
  text-shadow: 0 0 20px #ff4757, 0 0 40px #ff4757, 4px 4px 12px rgba(0,0,0,0.9);
  background: rgba(255,71,87,0.1);
  border-color: rgba(255,71,87,0.5);
}

h1 {
  font-size: 3rem;
  font-family: 'Dancing Script', cursive;
  animation: fadeInDown 2s ease;
}

.hidden {
  display: none;
}

.wish {
  font-size: clamp(2rem, 6vw, 4rem);
  font-family: 'Great Vibes', cursive;
  color: #ffffff;
  text-shadow: 3px 3px 8px rgba(0,0,0,0.9), 0 0 20px rgba(255,255,255,0.6);
  animation: fadeInUp 2s ease forwards;
  background: rgba(0,0,0,0.3);
  padding: 1rem 2rem;
  border-radius: 20px;
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255,255,255,0.2);
  margin-bottom: 2rem;
  line-height: 1.3;
  text-align: center;
  white-space: nowrap;
  max-width: 90vw;
}

/* Special handling for text wrapping */
@media (max-width: 600px) {
  .wish {
    white-space: normal;
    word-spacing: 0.2em;
  }

  .wish::after {
    content: "";
    display: block;
    width: 0;
    height: 0;
  }
}

.message {
  font-size: clamp(1.2rem, 3vw, 2rem);
  font-family: 'Poppins', sans-serif;
  color: #ffffff;
  text-shadow: 2px 2px 6px rgba(0,0,0,0.9);
  animation: fadeInUp 2s ease 0.5s forwards;
  background: rgba(0,0,0,0.25);
  padding: 1rem 1.5rem;
  border-radius: 15px;
  backdrop-filter: blur(4px);
  border: 1px solid rgba(255,255,255,0.15);
  margin-top: 1rem;
  line-height: 1.4;
  text-align: center;
  max-width: 90vw;
}

/* Scroll-based Content Styles */
.scroll-content {
  height: 100vh;
  overflow-y: auto;
  scroll-behavior: smooth;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.scroll-content::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.memory-section {
  height: 100vh;
  display: none;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: 2rem;
  position: relative;
  opacity: 0;
  transform: translateY(50px) scale(0.9);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.message-section {
  height: 100vh;
  display: none;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: 2rem;
  opacity: 0;
  transform: translateY(50px) scale(0.9);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.memory-section h2 {
  font-size: clamp(2rem, 6vw, 4rem);
  font-family: 'Great Vibes', cursive;
  color: #ffffff;
  text-shadow: 3px 3px 8px rgba(0,0,0,0.8), 0 0 20px rgba(255,255,255,0.5);
  margin-bottom: 1rem;
  background: rgba(0,0,0,0.25);
  padding: 1rem 2rem;
  border-radius: 20px;
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255,255,255,0.15);
  transition: all 0.6s ease;
  transform: translateY(20px);
  opacity: 0;
}

.memory-section p {
  font-size: clamp(1.2rem, 3vw, 2rem);
  font-family: 'Poppins', sans-serif;
  color: #ffffff;
  text-shadow: 2px 2px 6px rgba(0,0,0,0.8);
  background: rgba(0,0,0,0.15);
  padding: 1rem 1.5rem;
  border-radius: 15px;
  backdrop-filter: blur(3px);
  border: 1px solid rgba(255,255,255,0.08);
  transition: all 0.6s ease 0.2s;
  transform: translateY(20px);
  opacity: 0;
}

.memory-section.active h2,
.message-section.active h2 {
  transform: translateY(0);
  opacity: 1;
}

.memory-section.active p,
.message-section.active p {
  transform: translateY(0);
  opacity: 1;
}

/* Photo Background Carousel for Content */
.photo-bg-carousel {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  overflow: hidden;
}

.photo-bg-slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  opacity: 0;
  transition: opacity 1s ease-in-out;
}

.photo-bg-slide.active {
  opacity: 0.7;
}

/* Page Navigation Indicators */
.page-indicators {
  position: fixed;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 100;
  display: none;
  flex-direction: column;
  gap: 10px;
  opacity: 0;
  transition: opacity 0.5s ease;
}

.page-indicators.show {
  display: flex;
  opacity: 1;
}

.page-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.4);
  transition: all 0.3s ease;
  cursor: pointer;
}

.page-dot.active {
  background: rgba(255, 255, 255, 0.9);
  transform: scale(1.2);
}

.page-dot:hover {
  background: rgba(255, 255, 255, 0.7);
}

/* Background Elements */
.background-elements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 0;
}

.hearts, .stars {
  position: absolute;
  width: 100%;
  height: 100%;
  background-image: url('https://www.transparenttextures.com/patterns/hearts.png');
  background-size: 200px;
  opacity: 0.06;
  animation: slowScroll 60s linear infinite;
}

.stars {
  background-image: url('https://www.transparenttextures.com/patterns/stardust.png');
  animation-duration: 80s;
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(40px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-40px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Removed pulse animation */

@keyframes slowScroll {
  0% { background-position: 0 0; }
  100% { background-position: 0 1000px; }
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes finalCountdown {
  0%, 100% {
    transform: scale(1);
    text-shadow: 0 0 20px #ff4757, 0 0 40px #ff4757, 4px 4px 12px rgba(0,0,0,0.9);
  }
  50% {
    transform: scale(1.05);
    text-shadow: 0 0 30px #ff4757, 0 0 60px #ff4757, 0 0 80px #ff4757, 4px 4px 12px rgba(0,0,0,0.9);
  }
}

/* Background Image Carousel */
.bg-carousel {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  overflow: hidden;
  transition: opacity 1s ease-out;
}

.bg-slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  opacity: 0;
  transition: opacity 2s ease-in-out;
}

.bg-slide.active {
  opacity: 0.3;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .container {
    padding: 1rem 0.5rem;
  }

  #countdown-container h1 {
    font-size: clamp(1.5rem, 6vw, 3rem);
    margin-bottom: 1rem;
    padding: 0.8rem 1.5rem;
  }

  #countdown {
    font-size: clamp(2rem, 12vw, 6rem);
    margin: 1rem 0;
    padding: 1rem 2rem;
  }

  #countdown.final-minute {
    font-size: clamp(3rem, 16vw, 8rem);
  }

  .memory-section h2 {
    font-size: clamp(1.5rem, 5vw, 3rem);
    padding: 0.6rem 1.2rem;
    backdrop-filter: blur(6px);
  }

  .memory-section p {
    font-size: clamp(1rem, 2.5vw, 1.5rem);
    padding: 0.6rem 1rem;
    backdrop-filter: blur(4px);
  }

  .wish {
    font-size: clamp(1.8rem, 5vw, 3rem);
    white-space: normal;
    line-height: 1.3;
    padding: 0.8rem 1.5rem;
    backdrop-filter: blur(6px);
  }

  .message {
    font-size: clamp(1rem, 2.5vw, 1.5rem);
    padding: 0.8rem 1.2rem;
    backdrop-filter: blur(4px);
  }
}

@media (max-width: 480px) {
  #countdown-container h1 {
    font-size: clamp(1.2rem, 5vw, 2.5rem);
    padding: 0.6rem 1rem;
  }

  #countdown {
    font-size: clamp(1.8rem, 10vw, 4rem);
    padding: 0.8rem 1.5rem;
  }

  #countdown.final-minute {
    font-size: clamp(2.5rem, 14vw, 6rem);
  }

  .memory-section h2 {
    font-size: clamp(1.2rem, 4vw, 2.5rem);
    padding: 0.5rem 0.8rem;
    backdrop-filter: blur(4px);
  }

  .memory-section p {
    font-size: clamp(0.9rem, 2vw, 1.2rem);
    padding: 0.5rem 0.8rem;
    backdrop-filter: blur(3px);
  }

  .memory-section, .message-section {
    padding: 1rem;
  }

  .wish {
    font-size: clamp(1.5rem, 4vw, 2.5rem);
    padding: 0.6rem 1rem;
    backdrop-filter: blur(4px);
    white-space: normal;
    line-height: 1.4;
  }

  .message {
    font-size: clamp(0.9rem, 2vw, 1.2rem);
    padding: 0.6rem 1rem;
    backdrop-filter: blur(3px);
  }
}
